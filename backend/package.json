{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "mongoose": "^8.15.0"}, "devDependencies": {"nodemon": "^3.1.10"}}