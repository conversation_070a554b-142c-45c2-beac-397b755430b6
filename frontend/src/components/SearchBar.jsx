import { useState, useEffect, useCallback, useRef } from "react";
import PropTypes from "prop-types";
import "../styles/SearchBar.css";

const SearchBar = ({ onSearch }) => {
	const [searchTerm, setSearchTerm] = useState("");
	const [lastSearchTerm, setLastSearchTerm] = useState("");
	const debounceTimeoutRef = useRef(null);

	// Debounced search function
	const debouncedSearch = useCallback(
		(term) => {
			// Clear any existing timeout
			if (debounceTimeoutRef.current) {
				clearTimeout(debounceTimeoutRef.current);
			}

			debounceTimeoutRef.current = setTimeout(() => {
				// Only make API call if the search term has actually changed
				if (term !== lastSearchTerm) {
					onSearch(term);
					setLastSearchTerm(term);
				}
			}, 300);
		},
		[onSearch, lastSearchTerm],
	);

	// Effect to handle debounced search
	useEffect(() => {
		debouncedSearch(searchTerm);

		// Cleanup function
		return () => {
			if (debounceTimeoutRef.current) {
				clearTimeout(debounceTimeoutRef.current);
			}
		};
	}, [searchTerm, debouncedSearch]);

	const handleInputChange = (e) => {
		setSearchTerm(e.target.value);
	};

	const handleClear = () => {
		setSearchTerm("");
		// Only trigger search if we actually had a search term before
		if (lastSearchTerm !== "") {
			onSearch("");
			setLastSearchTerm("");
		}
	};

	return (
		<div className="search-bar">
			<div className="search-input-container">
				<input
					type="text"
					placeholder="Search credit card offers..."
					value={searchTerm}
					onChange={handleInputChange}
					className="search-input"
				/>
				{searchTerm && (
					<button type="button" className="clear-button" onClick={handleClear}>
						×
					</button>
				)}
			</div>
		</div>
	);
};

SearchBar.propTypes = {
	onSearch: PropTypes.func.isRequired,
};

export default SearchBar;
