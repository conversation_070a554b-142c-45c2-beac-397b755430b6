import { useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import "../styles/SearchBar.css";

const SearchBar = ({ onSearch, value = "" }) => {
	const [localSearchTerm, setLocalSearchTerm] = useState(value);
	const debounceTimeoutRef = useRef(null);
	const lastSearchedTermRef = useRef("");

	// Sync local state with prop value when it changes externally
	useEffect(() => {
		setLocalSearchTerm(value);
	}, [value]);

	// Effect to handle debounced search
	useEffect(() => {
		// Clear any existing timeout
		if (debounceTimeoutRef.current) {
			clearTimeout(debounceTimeoutRef.current);
		}

		// Set new timeout
		debounceTimeoutRef.current = setTimeout(() => {
			// Only make API call if the search term has actually changed
			if (localSearchTerm !== lastSearchedTermRef.current) {
				onSearch(localSearchTerm);
				lastSearchedTermRef.current = localSearchTerm;
			}
		}, 300);

		// Cleanup function
		return () => {
			if (debounceTimeoutRef.current) {
				clearTimeout(debounceTimeoutRef.current);
			}
		};
	}, [localSearchTerm, onSearch]);

	const handleInputChange = (e) => {
		setLocalSearchTerm(e.target.value);
	};

	const handleClear = () => {
		setLocalSearchTerm("");
		// The useEffect will handle the debounced search with empty term
	};

	return (
		<div className="search-bar">
			<div className="search-input-container">
				<input
					type="text"
					placeholder="Search credit card offers..."
					value={localSearchTerm}
					onChange={handleInputChange}
					className="search-input"
				/>
				{localSearchTerm && (
					<button type="button" className="clear-button" onClick={handleClear}>
						×
					</button>
				)}
			</div>
		</div>
	);
};

SearchBar.propTypes = {
	onSearch: PropTypes.func.isRequired,
	value: PropTypes.string,
};

export default SearchBar;
