import { useState, useEffect, useCallback } from "react";
import PropTypes from "prop-types";
import "../styles/SearchBar.css";

const SearchBar = ({ onSearch }) => {
	const [searchTerm, setSearchTerm] = useState("");

	// Debounced search function
	const debouncedSearch = useCallback(
		(term) => {
			const timeoutId = setTimeout(() => {
				onSearch(term);
			}, 300);
			return timeoutId;
		},
		[onSearch],
	);

	// Effect to handle debounced search
	useEffect(() => {
		const timeoutId = debouncedSearch(searchTerm);
		return () => clearTimeout(timeoutId);
	}, [searchTerm, debouncedSearch]);

	const handleInputChange = (e) => {
		setSearchTerm(e.target.value);
	};

	const handleClear = () => {
		setSearchTerm("");
	};

	return (
		<div className="search-bar">
			<div className="search-input-container">
				<input
					type="text"
					placeholder="Search credit card offers..."
					value={searchTerm}
					onChange={handleInputChange}
					className="search-input"
				/>
				{searchTerm && (
					<button type="button" className="clear-button" onClick={handleClear}>
						×
					</button>
				)}
			</div>
		</div>
	);
};

SearchBar.propTypes = {
	onSearch: PropTypes.func.isRequired,
};

export default SearchBar;
