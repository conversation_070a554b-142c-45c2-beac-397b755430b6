import { useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import "../styles/SearchBar.css";

const SearchBar = ({ onSearch }) => {
	const [searchTerm, setSearchTerm] = useState("");
	const debounceTimeoutRef = useRef(null);
	const lastSearchedTermRef = useRef("");

	// Effect to handle debounced search
	useEffect(() => {
		// Clear any existing timeout
		if (debounceTimeoutRef.current) {
			clearTimeout(debounceTimeoutRef.current);
		}

		// Set new timeout
		debounceTimeoutRef.current = setTimeout(() => {
			// Only make API call if the search term has actually changed
			if (searchTerm !== lastSearchedTermRef.current) {
				onSearch(searchTerm);
				lastSearchedTermRef.current = searchTerm;
			}
		}, 300);

		// Cleanup function
		return () => {
			if (debounceTimeoutRef.current) {
				clearTimeout(debounceTimeoutRef.current);
			}
		};
	}, [searchTerm, onSearch]);

	const handleInputChange = (e) => {
		setSearchTerm(e.target.value);
	};

	const handleClear = () => {
		setSearchTerm("");
		// The useEffect will handle the debounced search with empty term
	};

	return (
		<div className="search-bar">
			<div className="search-input-container">
				<input
					type="text"
					placeholder="Search credit card offers..."
					value={searchTerm}
					onChange={handleInputChange}
					className="search-input"
				/>
				{searchTerm && (
					<button type="button" className="clear-button" onClick={handleClear}>
						×
					</button>
				)}
			</div>
		</div>
	);
};

SearchBar.propTypes = {
	onSearch: PropTypes.func.isRequired,
};

export default SearchBar;
