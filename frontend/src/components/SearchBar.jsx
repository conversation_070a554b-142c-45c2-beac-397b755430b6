import { useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import "../styles/SearchBar.css";

const SearchBar = ({ onSearch, value = "" }) => {
	const [localSearchTerm, setLocalSearchTerm] = useState(value);
	const debounceTimeoutRef = useRef(null);
	const lastSearchedTermRef = useRef("");
	const inputRef = useRef(null);
	const isUserTypingRef = useRef(false);

	// Sync local state with prop value only when user is not actively typing
	useEffect(() => {
		if (!isUserTypingRef.current && value !== localSearchTerm) {
			setLocalSearchTerm(value);
		}
	}, [value, localSearchTerm]);

	// Effect to handle debounced search
	useEffect(() => {
		// Clear any existing timeout
		if (debounceTimeoutRef.current) {
			clearTimeout(debounceTimeoutRef.current);
		}

		// Set new timeout
		debounceTimeoutRef.current = setTimeout(() => {
			// Only make API call if the search term has actually changed
			if (localSearchTerm !== lastSearchedTermRef.current) {
				onSearch(localSearchTerm);
				lastSearchedTermRef.current = localSearchTerm;
			}
			// Reset typing flag after search is complete
			isUserTypingRef.current = false;
		}, 300);

		// Cleanup function
		return () => {
			if (debounceTimeoutRef.current) {
				clearTimeout(debounceTimeoutRef.current);
			}
		};
	}, [localSearchTerm, onSearch]);

	const handleInputChange = (e) => {
		isUserTypingRef.current = true;
		setLocalSearchTerm(e.target.value);
	};

	const handleClear = () => {
		setLocalSearchTerm("");
		isUserTypingRef.current = false;
		// Focus the input after clearing
		if (inputRef.current) {
			inputRef.current.focus();
		}
	};

	return (
		<div className="search-bar">
			<div className="search-input-container">
				<input
					ref={inputRef}
					type="text"
					placeholder="Search credit card offers..."
					value={localSearchTerm}
					onChange={handleInputChange}
					className="search-input"
				/>
				{localSearchTerm && (
					<button type="button" className="clear-button" onClick={handleClear}>
						×
					</button>
				)}
			</div>
		</div>
	);
};

SearchBar.propTypes = {
	onSearch: PropTypes.func.isRequired,
	value: PropTypes.string,
};

export default SearchBar;
