import axios from "axios";

const API_URL = "http://localhost:3000/api";

// Create axios instance
const api = axios.create({
	baseURL: API_URL,
	headers: {
		"Content-Type": "application/json",
	},
});

// Get all credit card offers
export const getAllCreditCardOffers = async (filters = {}) => {
	try {
		const { bank, category, search } = filters;
		const params = {};

		// Handle bank filter (array or string)
		if (Array.isArray(bank) && bank.length > 0) {
			params.bank = bank.join(",");
		} else if (bank && !Array.isArray(bank)) {
			params.bank = bank; // Support for single string bank filter
		}

		// Handle category filter (array or string)
		if (Array.isArray(category) && category.length > 0) {
			params.category = category.join(",");
		} else if (category && !Array.isArray(category)) {
			params.category = category; // Support for single string category filter
		}

		if (search) params.search = search;

		const response = await api.get("/credit-card-offers", { params });
		return response.data;
	} catch (error) {
		console.error("Error fetching credit card offers:", error);
		throw error;
	}
};

// Get all available banks
export const getAllBanks = async () => {
	try {
		const response = await api.get("/banks");
		return response.data;
	} catch (error) {
		console.error("Error fetching banks:", error);
		throw error;
	}
};

// Get all available categories
export const getAllCategories = async () => {
	try {
		const response = await api.get("/categories");
		return response.data;
	} catch (error) {
		console.error("Error fetching categories:", error);
		throw error;
	}
};

// Get a specific credit card offer by ID
export const getCreditCardOfferById = async (id) => {
	try {
		const response = await api.get(`/credit-card-offers/${id}`);
		return response.data;
	} catch (error) {
		console.error(`Error fetching credit card offer with ID ${id}:`, error);
		throw error;
	}
};

export default api;
