import { useState, useEffect } from "react";
import { getAllCreditCardOffers, getAllBanks, getAllCategories } from "../services/api";
import CreditCardOffer from "../components/CreditCardOffer";
import BankFilter from "../components/BankFilter";
import CategoryFilter from "../components/CategoryFilter";
import SearchBar from "../components/SearchBar";
import "../styles/HomePage.css";

const HomePage = () => {
	const [offers, setOffers] = useState([]);
	const [banks, setBanks] = useState([]);
	const [categories, setCategories] = useState([]);
	const [selectedBank, setSelectedBank] = useState([]); // Changed initial state
	const [selectedCategory, setSelectedCategory] = useState([]); // Changed initial state
	const [searchTerm, setSearchTerm] = useState("");
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(null);

	// Fetch all banks
	useEffect(() => {
		const fetchBanks = async () => {
			try {
				const data = await getAllBanks();
				setBanks(data);
			} catch (err) {
				console.error("Failed to fetch banks:", err);
			}
		};

		fetchBanks();
	}, []);

	// Fetch all categories
	useEffect(() => {
		const fetchCategories = async () => {
			try {
				const data = await getAllCategories();
				setCategories(data);
			} catch (err) {
				console.error("Failed to fetch categories:", err);
			}
		};

		fetchCategories();
	}, []);

	// Fetch offers based on filters
	useEffect(() => {
		const fetchOffers = async () => {
			setLoading(true);
			try {
				const filters = {
					bank: selectedBank,
					category: selectedCategory,
					search: searchTerm,
				};

				const data = await getAllCreditCardOffers(filters);
				setOffers(data);
				setLoading(false);
			} catch (err) {
				setError("Failed to fetch credit card offers. Please try again later.");
				setLoading(false);
			}
		};

		fetchOffers();
	}, [selectedBank, selectedCategory, searchTerm]);

	if (loading) {
		return <div className="loading">Loading credit card offers...</div>;
	}

	if (error) {
		return <div className="error">{error}</div>;
	}

	const handleBankSelect = (bank) => {
		setSelectedBank(bank);
	};

	const handleCategorySelect = (category) => {
		setSelectedCategory(category);
	};

	const handleSearch = (term) => {
		setSearchTerm(term);
	};

	// Create filter description text
	const getFilterDescription = () => {
		const filters = [];
		if (selectedBank.length > 0) filters.push(`Banks: ${selectedBank.join(", ")}`);
		if (selectedCategory.length > 0) filters.push(`Categories: ${selectedCategory.join(", ")}`);
		if (searchTerm) filters.push(`Search: "${searchTerm}"`);

		if (filters.length === 0) return "Showing all offers."; // Default message
		return `Filtered by ${filters.join(" | ")}`; // Using pipe as separator
	};

	return (
		<div className="home-page">
			<header className="header">
				<h1>Credit Card Offers in Sri Lanka</h1>
				<p>Compare the best credit card offers from various banks in Sri Lanka</p>
			</header>

			<div className="filters-and-search-row">
				{" "}
				{/* New wrapper div */}
				<SearchBar onSearch={handleSearch} />
				<BankFilter banks={banks} selectedBanks={selectedBank} onBankSelect={handleBankSelect} />{" "}
				{/* Prop name changed */}
				<CategoryFilter
					categories={categories}
					selectedCategories={selectedCategory} // Prop name changed
					onCategorySelect={handleCategorySelect}
				/>
			</div>

			{/* Display filter description if any filter is active */}

			<div className="offers-container">
				{offers.length === 0 ? (
					<p className="no-offers">No credit card offers available for the selected filters.</p>
				) : (
					offers.map((offer) => <CreditCardOffer key={offer.id} offer={offer} />)
				)}
			</div>
		</div>
	);
};

export default HomePage;
