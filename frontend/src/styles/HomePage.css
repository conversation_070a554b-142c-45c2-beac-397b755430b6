.home-page {
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
}

.header {
	text-align: center;
	margin-bottom: 40px;
}

.header h1 {
	color: #333;
	margin-bottom: 10px;
}

.header p {
	color: #666;
	font-size: 1.1rem;
}

/* New styles for the combined filter and search row */
.filters-and-search-row {
	display: flex;
	flex-wrap: wrap; /* Allow items to wrap to the next line on smaller screens */
	gap: 20px; /* Space between items */
	margin-bottom: 30px;
	align-items: flex-start; /* Align items to the top */
}

/* Styling for children of filters-and-search-row */
/* Assuming SearchBar has a wrapper with class .search-bar-container */
/* BankFilter has .bank-filter, <PERSON><PERSON><PERSON><PERSON> has .category-filter */
.filters-and-search-row > .search-bar-container, /* Target SearchBar's wrapper */
.filters-and-search-row > .bank-filter,          /* Target BankFilter's wrapper */
.filters-and-search-row > .category-filter {     /* Target CategoryFilter's wrapper */
	flex: 1 1 200px; /* Grow, shrink, with a base width of 200px */
	min-width: 180px; /* Prevent them from becoming too narrow */
}


.filter-description {
	text-align: center;
	margin-bottom: 20px;
	padding: 10px;
	background-color: #f8f9fa;
	border-radius: 4px;
	border: 1px solid #e0e0e0;
	color: #495057;
	font-size: 0.95rem;
}

.offers-container {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
	gap: 20px;
}

.loading,
.error,
.no-offers {
	text-align: center;
	padding: 40px;
	font-size: 1.2rem;
	color: #666;
}

.error {
	color: #dc3545;
}

@media (max-width: 768px) {
	/* No specific styles needed for .filters-and-search-row for wrapping, flex-wrap handles it. */
	/* Individual filter/search components will stack due to flex-wrap and their flex-basis. */

	.offers-container {
		grid-template-columns: 1fr;
	}
}
