.home-page {
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
}

.header {
	text-align: center;
	margin-bottom: 40px;
}

.header h1 {
	color: #333;
	margin-bottom: 10px;
}

.header p {
	color: #666;
	font-size: 1.1rem;
}

/* New styles for the combined filter and search row */
.filters-and-search-row {
	display: flex;
	flex-wrap: wrap; /* Allow items to wrap to the next line on smaller screens */
	gap: 15px; /* Space between items */
	margin-bottom: 30px;
	align-items: stretch; /* Make all items the same height */
}

/* Styling for children of filters-and-search-row */
.filters-and-search-row > .search-bar,
.filters-and-search-row > .bank-filter,
.filters-and-search-row > .category-filter {
	flex: 1 1 250px; /* Grow, shrink, with a base width of 250px */
	min-width: 200px; /* Prevent them from becoming too narrow */
	display: flex;
	flex-direction: column;
}

/* Ensure all filter components have consistent styling */
.filters-and-search-row > .search-bar .search-input-container,
.filters-and-search-row > .bank-filter .filter-container,
.filters-and-search-row > .category-filter .filter-container {
	flex: 1;
}

.filter-description {
	text-align: center;
	margin-bottom: 20px;
	padding: 10px;
	background-color: #f8f9fa;
	border-radius: 4px;
	border: 1px solid #e0e0e0;
	color: #495057;
	font-size: 0.95rem;
}

.offers-container {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
	gap: 20px;
}

.loading,
.error,
.no-offers {
	text-align: center;
	padding: 40px;
	font-size: 1.2rem;
	color: #666;
}

.error {
	color: #dc3545;
}

@media (max-width: 768px) {
	.filters-and-search-row {
		flex-direction: column;
		gap: 15px;
	}

	.filters-and-search-row > .search-bar,
	.filters-and-search-row > .bank-filter,
	.filters-and-search-row > .category-filter {
		flex: 1 1 auto;
		min-width: unset;
	}

	.offers-container {
		grid-template-columns: 1fr;
	}
}
