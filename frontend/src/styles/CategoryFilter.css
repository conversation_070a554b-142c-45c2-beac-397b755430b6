/* Styles for the CategoryFilter component */

.category-filter {
	/* This container's sizing is primarily controlled by .filters-and-search-row > .category-filter in HomePage.css */
	/* Add any specific styles for the .category-filter div if needed beyond flex item behavior */
}

.filter-title {
	/* Styles for the "Filter by Category" title */
	/* This title is still present in CategoryFilter.jsx */
	margin-bottom: 8px; /* Reduced margin for a tighter layout with react-select */
	text-align: left; /* Align with the select component */
	color: #333;
	font-size: 1rem; /* Adjusted font size */
}

.category-select-container .category-select__control {
	/* Example: Adjust min-height or other control properties if needed */
	/* min-height: 38px; */
}

.category-select-container .category-select__menu {
	/* Example: Adjust menu z-index if it conflicts with other elements */
	/* z-index: 1000; */
}

/* All old styles related to buttons have been removed. */
/* The react-select component will use its default styling or can be further customized here */
/* by targeting its specific classes like .category-select__control, .category-select__menu, etc. */
/* The .category-select-container class was added to the Select component in CategoryFilter.jsx */
/* The .category-select__ prefix is the default classNamePrefix for react-select. */
