/* Styles for the BankFilter component */

.bank-filter {
	/* This container's sizing is primarily controlled by .filters-and-search-row > .bank-filter in HomePage.css */
	/* Add any specific styles for the .bank-filter div if needed beyond flex item behavior */
	/* For example, if it needed its own padding or a border: */
	/* padding: 0; */
}

.filter-title {
	/* Styles for the "Filter by Bank" title */
	/* This title is still present in BankFilter.jsx */
	margin-bottom: 8px; /* Reduced margin for a tighter layout with react-select */
	text-align: left; /* Align with the select component */
	color: #333;
	font-size: 1rem; /* Adjusted font size */
}

.bank-select-container .bank-select__control {
	/* Example: Adjust min-height or other control properties if needed */
	/* min-height: 38px; */
}

.bank-select-container .bank-select__menu {
	/* Example: Adjust menu z-index if it conflicts with other elements */
	/* z-index: 1000; */
}

/* All old styles related to buttons have been removed. */
/* The react-select component will use its default styling or can be further customized here */
/* by targeting its specific classes like .bank-select__control, .bank-select__menu, etc. */
/* The .bank-select-container class was added to the Select component in BankFilter.jsx */
/* The .bank-select__ prefix is the default classNamePrefix for react-select. */
