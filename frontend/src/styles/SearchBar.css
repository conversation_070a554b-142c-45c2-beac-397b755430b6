.search-bar {
	/* Removed margin-bottom as it's now controlled by the parent container */
}

.search-input-container {
	position: relative;
	width: 100%;
}

.search-input {
	width: 100%;
	padding: 12px 40px 12px 15px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 1rem;
	outline: none;
	transition: border-color 0.3s;
	box-sizing: border-box;
	height: 44px; /* Fixed height to match react-select components */
}

.search-input:focus {
	border-color: #007bff;
	box-shadow: 0 0 0 1px #007bff;
}

.clear-button {
	position: absolute;
	right: 10px;
	top: 50%;
	transform: translateY(-50%);
	background: none;
	border: none;
	font-size: 1.5rem;
	color: #999;
	cursor: pointer;
	padding: 0;
	line-height: 1;
	z-index: 1;
}

.clear-button:hover {
	color: #666;
}

@media (max-width: 768px) {
	.search-input {
		margin-bottom: 0;
	}
}
