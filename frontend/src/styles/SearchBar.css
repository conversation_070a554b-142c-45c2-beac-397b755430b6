.search-bar {
  margin-bottom: 30px;
}

.search-bar form {
  display: flex;
  max-width: 600px;
  margin: 0 auto;
}

.search-input-container {
  position: relative;
  flex-grow: 1;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.3s;
}

.search-input:focus {
  border-color: #007bff;
}

.clear-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #999;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.clear-button:hover {
  color: #666;
}

.search-button {
  padding: 12px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.search-button:hover {
  background-color: #0056b3;
}

@media (max-width: 576px) {
  .search-bar form {
    flex-direction: column;
  }
  
  .search-input {
    border-radius: 4px;
    margin-bottom: 10px;
  }
  
  .search-button {
    border-radius: 4px;
    width: 100%;
  }
}
